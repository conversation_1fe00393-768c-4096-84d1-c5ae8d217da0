import { FormSchema } from "@/lib/types/form";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "../ui/select";
import { useEffect } from "react";
import { cn } from "@/lib/utils";

interface FormMetaFieldsProps {
  form: FormSchema;
  onFormChange: (field: keyof FormSchema, value: any) => void;
}

export default function FormMetaFields({
  form,
  onFormChange,
}: Readonly<FormMetaFieldsProps>) {

  useEffect(() => {
    onFormChange("formType", "APPLICATION")
  }, [])

  // Check if required fields are empty
  const isNameEmpty = !form.name?.trim();
  const isProjectTypeEmpty = !form.projectType?.trim();

  return (
    <div className="grid gap-6 sm:grid-cols-2">
      <div className="grid gap-2">
        <Label htmlFor="name" className="flex items-center gap-1">
          Form Name
          <span className="text-red-500">*</span>
        </Label>
        <Input
          id="name"
          value={form.name}
          onChange={(e) => onFormChange("name", e.target.value)}
          placeholder="Enter form name"
          className={cn(
            "w-full",
            isNameEmpty && "border-red-300 focus:border-red-500 focus:ring-red-500"
          )}
        />
        {isNameEmpty && (
          <p className="text-sm text-red-600">Form name is required</p>
        )}
      </div>
      <div className="grid gap-2">
        <Label htmlFor="description">Description</Label>
        <Textarea
          id="description"
          value={form.description ?? ""}
          onChange={(e) => onFormChange("description", e.target.value)}
          placeholder="Enter form description"
          className="h-[38px] min-h-[38px] w-full resize-none"
        />
      </div>

      <div className="grid gap-2">
        <Label htmlFor="projectType" className="flex items-center gap-1">
          Project Type
          <span className="text-red-500">*</span>
        </Label>
        <Select
          value={form.projectType ?? ""}
          onValueChange={(value) => onFormChange("projectType", value)}
        >
          <SelectTrigger
            id="projectType"
            className={cn(
              isProjectTypeEmpty && "border-red-300 focus:border-red-500 focus:ring-red-500"
            )}
          >
            <SelectValue placeholder="Select project type" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="CAPITAL">CAPITAL</SelectItem>
            <SelectItem value="REVENUE">REVENUE</SelectItem>
          </SelectContent>
        </Select>
        {isProjectTypeEmpty && (
          <p className="text-sm text-red-600">Project type is required</p>
        )}
      </div>
      <div className="grid gap-2 invisible">
        <Label htmlFor="description">Form Type</Label>
        <Select
          value={form.formType ?? "APPLICATION"}
          onValueChange={(value) => onFormChange("formType", value)}

        >
          <SelectTrigger id="formType">
            <SelectValue placeholder="Select form type" />
          </SelectTrigger>
          <SelectContent>
            {/* <SelectItem value="MR">MR</SelectItem> */}
            <SelectItem value="APPLICATION">APPLICATION</SelectItem>
          </SelectContent>
        </Select>

      </div>
    </div>
  );
}
